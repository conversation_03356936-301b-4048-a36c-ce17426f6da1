import type { Metada<PERSON> } from "next";
import "./globals.css";
import { urw, gravtrac } from "./fonts";
import AuthInitializer from "@/components/AuthInitializer";
import DesktopGate from "@/components/DesktopGate";

export const dynamic = "force-dynamic";

export const metadata: Metadata = {
  title: "Netflix - 100 PLUS",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${urw.variable} ${gravtrac.variable} bg-black antialiased`}
      >
        <AuthInitializer />
        {/* <DesktopGate> */}
        {children}
        {/* </DesktopGate> */}
      </body>
    </html>
  );
}
