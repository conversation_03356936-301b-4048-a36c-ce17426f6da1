@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --font-sans: var(--font-urw), system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>,
    <PERSON><PERSON>, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", sans-serif;
  --font-heading: var(--font-gravtrac), var(--font-urw), system-ui, sans-serif;
}

html,
body {
  height: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
}

.font-heading {
  font-family: var(--font-heading);
  letter-spacing: 0.02em;
}

html,
body,
button {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
