"use client";

import { useAuthStore } from "@/store/authStore";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function ProtectedRoute({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, isLoading, checkAuth, user } = useAuthStore();
  const router = useRouter();
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    // Selalu lakukan pengecekan autentikasi saat komponen dimount
    const performAuthCheck = async () => {
      if (!hasChecked) {
        await checkAuth();
        setHasChecked(true);
      }
    };

    performAuthCheck();
  }, [checkAuth, hasChecked]);

  useEffect(() => {
    // Redirect ke sign-in jika sudah selesai loading dan tidak terautentikasi
    if (hasChecked && !isLoading && !isAuthenticated) {
      router.replace("/sign-in");
    }
  }, [isAuthenticated, isLoading, router, hasChecked]);

  // <PERSON><PERSON><PERSON><PERSON> loading jika masih dalam proses pengecekan atau loading
  if (!hasChecked || isLoading) {
    return (
      <div className="flex items-center justify-center h-screen w-full bg-black text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
          <p>Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Jika tidak terautentikasi, tampilkan loading (akan redirect)
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-screen w-full bg-black text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
          <p>Redirecting to sign in...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
