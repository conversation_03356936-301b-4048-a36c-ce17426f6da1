"use client";

import { useAuthStore } from "@/store/authStore";
import { useEffect } from "react";

function AuthInitializer() {
  const checkAuth = useAuthStore((state) => state.checkAuth);

  useEffect(() => {
    // Hanya panggil sekali saat mount, tanpa dependency checkAuth
    // Tambahkan delay kecil untuk memastikan DOM sudah ready
    const timer = setTimeout(() => {
      checkAuth();
    }, 50);

    return () => clearTimeout(timer);
  }, []); // Empty dependency array

  return null;
}

export default AuthInitializer;
