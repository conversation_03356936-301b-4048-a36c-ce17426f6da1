import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// Routes yang memerlukan autentikasi
const protectedRoutes = ["/dashboard", "/profile"];

// Routes yang hanya bisa diakses oleh user yang belum login
const authRoutes = ["/sign-in", "/register", "/verify-otp"];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // TEMPORARY: Disable middleware untuk debugging
  // Mari kita biarkan client-side authentication yang menangani dulu
  console.log(`[Middleware] Processing: ${pathname}`);

  // Cek berbagai kemungkinan nama cookie yang digunakan backend
  const possibleTokenNames = [
    "accessToken",
    "refreshToken",
    "token",
    "auth-token",
    "jwt",
    "session",
    "connect.sid",
  ];

  // Cari token di cookies dengan berbagai nama yang mungkin
  let token = null;
  for (const tokenName of possibleTokenNames) {
    const cookieValue = request.cookies.get(tokenName);
    if (cookieValue) {
      token = cookieValue;
      break;
    }
  }

  const isAuthenticated = !!token;

  // Debug logging (hanya di development)
  if (process.env.NODE_ENV === "development") {
    console.log(
      `[Middleware] Path: ${pathname}, Authenticated: ${isAuthenticated}`
    );
    if (token) {
      console.log(
        `[Middleware] Found token: ${token.name}=${token.value?.substring(
          0,
          20
        )}...`
      );
    } else {
      console.log(
        `[Middleware] Available cookies:`,
        request.cookies.getAll().map((c) => c.name)
      );
    }
  }

  // TEMPORARY: Comment out redirects untuk debugging
  /*
  // Jika mengakses protected route tanpa token
  if (protectedRoutes.some((route) => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      console.log(`[Middleware] Redirecting to sign-in from ${pathname}`);
      const signInUrl = new URL("/sign-in", request.url);
      return NextResponse.redirect(signInUrl);
    }
  }

  // Jika mengakses auth route dengan token (sudah login)
  if (authRoutes.some((route) => pathname.startsWith(route))) {
    if (isAuthenticated) {
      console.log(`[Middleware] Redirecting to dashboard from ${pathname}`);
      const dashboardUrl = new URL("/dashboard", request.url);
      return NextResponse.redirect(dashboardUrl);
    }
  }
  */

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|images).*)",
  ],
};
