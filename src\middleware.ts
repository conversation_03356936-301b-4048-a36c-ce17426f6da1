import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Routes yang memerlukan autentikasi
const protectedRoutes = ['/dashboard', '/profile'];

// Routes yang hanya bisa diakses oleh user yang belum login
const authRoutes = ['/sign-in', '/register', '/verify-otp'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Cek apakah ada token di cookies
  const token = request.cookies.get('accessToken') || request.cookies.get('refreshToken');
  const isAuthenticated = !!token;

  // Jika mengakses protected route tanpa token
  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      const signInUrl = new URL('/sign-in', request.url);
      return NextResponse.redirect(signInUrl);
    }
  }

  // Jika mengakses auth route dengan token (sudah login)
  if (authRoutes.some(route => pathname.startsWith(route))) {
    if (isAuthenticated) {
      const dashboardUrl = new URL('/dashboard', request.url);
      return NextResponse.redirect(dashboardUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
  ],
};
