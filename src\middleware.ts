import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// TEMPORARY: Disable middleware completely untuk debugging
// Biarkan client-side authentication yang menangani sepenuhnya
export function middleware(request: NextRequest) {
  // Just pass through all requests without any authentication checks
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|images).*)",
  ],
};
